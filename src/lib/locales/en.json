{"time_ago.just_now": "Just now", "time_ago.minute": "minute", "time_ago.minutes": "minutes", "time_ago.hour": "hour", "time_ago.hours": "hours", "time_ago.day": "day", "time_ago.days": "days", "time_ago.week": "week", "time_ago.weeks": "weeks", "time_ago.month": "month", "time_ago.months": "months", "time_ago.year": "year", "time_ago.years": "years", "time_ago.ago": "ago", "day_monday": "Monday", "day_tuesday": "Tuesday", "day_wednesday": "Wednesday", "day_thursday": "Thursday", "day_friday": "Friday", "day_saturday": "Saturday", "day_sunday": "Sunday", "day_today": "Today", "day_yesterday": "Yesterday", "account": "Account", "away": "Away", "business": "General", "customers": "Customers", "dashboard": "Dashboard", "home": "Home", "knowledgeBase": "Knowledge Base", "language": "Language", "logout": "Log Out", "online": "Online", "settings": "Settings", "status": "Status", "tasks": "My Tasks", "teamManagement": "Team management", "tickets": "Tickets", "testing": "Testing", "uploadFiles": "Upload Files", "users": "Users", "business_settings_title": "General Settings", "business_settings_description": "Configure system preferences and manage application general settings.", "tab_system": "System", "tab_company": "Company", "tab_bot": "AI Chatbot", "tab_connection": "Connection", "appearance_title": "Website Appearance", "appearance_description": "Customize your website look and feel.", "upload_logo": "Upload Logo", "color_settings": "Color Settings", "modified": "modified", "saving": "Saving...", "save_logo": "Save Logo", "save_colors": "Save Colors", "dominant_color": "Dominant Color", "dominant_color_desc": "Used for main buttons, headers, and primary interactive elements.", "secondary_color": "Secondary Color", "secondary_color_desc": "Used for secondary buttons, backgrounds, and supporting elements.", "accent_color": "Accent Color", "accent_color_desc": "Used for highlights, notifications, and drawing attention to specific elements.", "recommended_size": "Recommended size", "unsaved_changes": "You have unsaved changes.", "timeout_settings": "Timeout Settings", "timeout_description": "Configure the system to automatically close tickets that remain unassigned after a period of inactivity.", "first_timeout": "First Timeout (minutes)", "first_timeout_desc": "Time before the first inactivity notification for an open ticket with no customer response.", "second_timeout": "Second Timeout (minutes)", "second_timeout_desc": "Time after the initial warning before closing an unassigned ticket with no customer response.", "logo_saved_success": "<PERSON><PERSON> saved successfully!", "click_and_drag": "Click to upload or drag and drop", "company_info_title": "Company Information", "company_info_description": "Manage your company details and business information.", "company_name_th": "Thai Company Name", "company_name_en": "English Company Name", "company_business": "Company Business", "business_type": "Business Type", "select_business_type": "Select business type", "save": "Save", "save_changes": "Save Changes", "changes_saved": "Changes saved successfully!", "chatbot_title": "Bot Configuration", "chatbot_description": "Customize your bot's personality and conversation style.", "chatbot_thai_name": "Thai Name", "chatbot_english_name": "English Name", "chatbot_role": "Role", "chatbot_gender": "Gender", "chatbot_style": "Conversation Style", "chatbot_type": "Conversation Type", "select_role": "Select Role", "select_gender": "Select Gender", "select_style": "Select Conversation Style", "select_type": "Select Conversation Type", "team_management": "Team Management", "team_management_description": "Configure tags, ticket transfer, and business hours.", "tab_user": "User", "tab_customer": "Customer", "tab_transfer": "Transfer", "partners": "Partners", "partners_description": "Manage your partners and collaborations.", "add_partner": "Add Partner", "enter_partner_name": "Enter Partner Name...", "enter_partner_code": "Enter Partner Abbreviation...", "update": "Update", "cancel": "Cancel", "confirm": "Confirm", "creating": "Creating...", "updating": "Updating...", "delete": "Delete", "next": "Next", "previous": "Previous", "no_partners": "No partners have been added at this time.", "select_color": "Select color", "departments": "Departments", "departments_desc": "Organize your content by departments or teams.", "add_department": "Add Department", "name": "Name", "name_desc": "Enter department name...", "code": "Abbreviation", "code_desc": "Enter department abbreviation...", "description": "Description", "description_desc": "Enter department description...", "no_departments": "No departments have been added at this time.", "specialized_tags": "Specialized Tags", "tag_members_by_role": "Tag members by role or expertise", "add_tag": "Add Tag", "enter_tag_name": "Enter tag name...", "tags_title": "Tags", "tags_description": "Create and manage tags to categorize customers.", "no_tags": "No tags have been added at this time.", "ticket_transfer_settings": "Ticket Transfer Settings", "ticket_transfer_description": "Configure how tickets are transferred and assigned.", "save_success": "Transfer settings saved successfully!", "unsaved_changes_warning": "You have unsaved changes to your transfer settings.", "step_1_title": "Role Assignment", "step_1_desc": "Assign tickets to agents based on their role.", "step_1_desc_remark": "This action will transfer the agent role only.", "step_2_title": "Department Matching", "step_2_desc": "Match tickets to agents in the appropriate department.", "enable_department": "Enable Department", "step_3_title": "Active Status Check", "step_3_desc": "Check if any agents are online and available.", "step_4_title": "Tag Matching", "step_4_desc": "Match tickets based on agent tags e.g, EV, PET, FIRE", "enable_tag": "Enable Tag", "step_5_title": "Additional Assignment Algorithms", "step_6_title": "Auto-Notification Settings", "step_6_desc": "Automatically notify customers when no admin responds within a specified time after ticket transfer.", "algorithm_frequent": "Frequently Assigned Agents", "algorithm_frequent_desc": "Agents who frequently handle similar tickets.", "algorithm_csat": "CSAT Score", "algorithm_csat_desc": "Agents with higher customer satisfaction ratings.", "algorithm_workload": "Workload Balancing", "algorithm_workload_desc": "Distribute tickets based on agent workload.", "default": "<PERSON><PERSON><PERSON>", "account_settings": "Account <PERSON><PERSON>", "account_settings_description": "Configure personal settings and details.", "tab_profile": "Profile", "tab_schedule": "Schedule", "tab_schedule_error_title": "Please fix the following errors:", "tab_schedule_error_start_time": "Start time must be before end time", "section_partners": "Partners", "section_departments": "Departments", "section_tags": "Tags", "your_info_title": "Your Information", "your_info_description": "Customize your information.", "first_name": "First name", "last_name": "Last name", "email": "Email", "phone_number": "Phone Number", "success_generic": "Your information has been updated successfully.", "error_generic": "Something went wrong. Please try again.", "work_shift_title": "Your work shift", "work_shift_description_1": "Individual work shift is used for assigning chats to agents, set up in automation hub, and does not affect login periods.", "work_shift_description_2": "Toggle off to configure user's work shift", "work_shift_update_success": "Work schedule updated successfully", "same_as_business_hours": "Same as business hours", "to": "to", "tickets_description": "Manage ticket assignments and statuses.", "view_my_tickets": "View My Tickets", "priority": "Priority", "sentiment": "Sentiment", "reset_filters": "Reset Filters", "search_placeholder": "Search tickets by, e.g., No., customer name, agent name, etc.", "tickets_total": "Total", "tickets_open": "Open", "tickets_assigned": "Assigned", "tickets_waiting": "Waiting", "tickets_closed": "Closed", "table_no": "No.", "table_status": "Status", "table_priority": "Priority", "table_sentiment": "Sentiment", "table_customer": "Customer name", "table_agent": "Agent", "table_time": "Created", "table_actions": "Actions", "table_updated_on": "Updated On", "table_updated_ago": "Last Updated", "table_no_ticket": "No ticket(s) to display", "table_unknown": "Unknown", "table_unclassified": "Unclassified", "transfer_ticket_title": "Transferring Ticket Ownership", "current_owner": "Current Owner:", "select_new_owner": "Select New Owner", "search": "Search", "warning": "Warning", "assign_offline_warning": "You are assigning this ticket to an offline user. They may not respond quickly.", "assign_away_warning": "You are assigning this ticket to a user who is away. Response may be delayed.", "transfer_ticket": "Transfer", "priority_modal_title": "Change Priority", "priority_modal_button": "Priority", "priority_select_label": "Select New Priority", "ticket_status": "Ticket Status", "current_status": "Current Status", "select_new_ticket_status": "Select New Ticket Status", "additional_info_required": "Additional information required for closing this ticket:", "select_case_type": "Select Case Type", "case_topic_multiple": "Case Topic (Select multiple)", "please_select_case_topic": "Please select at least one case topic.", "please_change_status": "Please select a different status or cancel.", "users_page_title": "Accounts", "users_page_description": "Manage user roles and access.", "filter_status": "Status", "filter_role": "Role", "filter_partner": "Partner", "filter_department": "Department", "filter_specialized_tag": "Specialized Tag", "filter_reset": "Reset Filters", "search_user_placeholder": "Search nickname or name...", "table_workload": "Workload", "table_name": "Name", "table_role": "Role", "table_partner": "Partner", "table_specialize_tag": "Specialized Tag", "table_department": "Department", "table_last_active": "Last Active", "no_users": "No user(s) to display", "label": "Labels", "busy": "Busy", "offline": "Offline", "active": "Active", "inactive": "Inactive", "all_status": "All Status", "create_account": "Create Account", "employee_id": "Account ID", "display_name": "Display name", "nickname": "Nickname", "username": "Username", "password": "Password", "confirm_password": "Confirm password", "change_password": "Change password", "current_password": "Current password", "current_password_placeholder": "Enter current password", "new_password": "New password", "new_password_placeholder": "Enter new password", "confirm_new_password": "Confirm new password", "confirm_new_password_placeholder": "Confirm new password", "submit": "Submit", "password_change_success": "Password changed successfully", "profile_change_success": "Profile updated successfully", "new_account": "New account", "total_members": "Total members", "new_members_30_days": "New members (Last 30 days)", "active_members": "Active members", "labels": "Labels", "filter_tag": "Tag", "table_tag": "Tag", "table_phone": "Phone Number", "table_platform": "Platform", "table_email": "Email", "table_created_on": "Created On", "table_no_data": "No customer(s) to display", "customers_search_placeholder": "Search customers by, e.g., No., name, phone number, etc.", "testing_page_title": "Chatbot Testing", "service_endpoints": "Service Endpoints", "faq_service": "FAQ Service", "example_message": "Example Message", "testing_section_title": "Testing", "testing_section_description": "Test chatbot responses using your knowledge base to ensure accurate and helpful answers.", "send": "Send", "clear": "Clear", "chatbot_response": "Chatbot Response", "reference_information": "Reference Information", "loading": "Loading", "note": "Note", "note_faq": "All-Features combine Customer Support, Product Search and Promotion", "reference_question": "Question", "reference_answer": "Answer", "type_message_here": "Type your message here...", "upload_documents": "Upload Documents", "documents": "Documents", "customer_support": "Customer Support", "promotion": "Promotion", "product": "Product", "trash": "Trash", "upload_files": "Upload files", "knowledge_base_description": "Organize documentation and helpful resources for your customer", "total": "Total", "business_hours": "Business Hours", "business_hours_description": "Set your chat's operating hours.", "business_hours_update_success": "Business hours updated successfully.", "business_hours_update_error": "Failed to update business hours.", "upload_title": "Upload Files", "select_category": "Select Category", "upload_file": "Upload File", "upload_image": "Select Image", "start_date": "Start Date", "end_date": "End Date", "description_upload_file": "Description (Optional)", "note_label": "Note", "note_admin_only": "Only Admin and Supervisor can upload documents", "note_upload_success": "Document was successfully uploaded!", "note_supported_format_customer": "Supported file formats: PDF, CSV, XLSX only", "note_supported_format_promotion": "Supported file formats: JPG, PNG, PDF only", "note_supported_format_product": "Supported file formats: XLSX, CSV only", "note_supported_image": "Images support only PNG, JPG", "upload_button": "Upload File", "uploading": "Uploading ...", "download_file": "Download File", "delete_permanently": "Delete Permanently", "line_settings": "LINE", "webhook_settings": "Line Webhook Settings", "line_channel_secret": "LINE Channel Secret", "line_access_token": "LINE Access Token", "webhook": "Webhook", "copy_webhook": "<PERSON><PERSON>ok", "webhook_note": "The secret key from your LINE Messaging API settings.", "access_token_note": "The access token from your LINE Messaging API settings.", "line_qr_code": "LINE QR Code", "line_oa": "LINE Official Account", "line_group": "LINE Group", "link": "Link", "copy_link": "Copy link", "save_qr": "Save QR Code", "upload_qr_note": "Upload QR Code (SVG, PNG, JPG or GIF)", "upload_qr": "Upload QR Code", "whatsapp_settings": "WhatsA<PERSON> Settings", "disabled_input": "Disabled input", "facebook_messenger": "Facebook Messenger", "detail": "Detail", "user_profile": "User Profile", "view_users_profile_memberships": "View user’s profile memberships", "user_number": "User Number", "line_account": "LINE account", "no_line_account": "No LINE account", "no_connect_line": "LINE account doesn't connect yet...", "last_active": "Last active", "work_information": "Work Information", "view_users_work-related_memberships": "View user’s work-related memberships", "role": "Role", "partner": "Partner", "department": "Department", "specialize_tag": "Specialize Tag", "no_specialize_tags": "No Specialize tags", "current_workload": "Current workload", "my_tasks": "My Tasks", "customer_name": "Customer name", "time": "Time", "updated_on": "Updated On", "no_tasks_assigned": "No tasks assigned", "policies": "Policies", "notes": "Notes", "memories": "Memories", "no_memory": "No memories have been added yet.", "export_customer_conversations": "Export Customer Conversations", "customer_details": "Customer Details", "no": "No.", "age": "Age", "social_platform": "Social Platform", "tag": "Tag", "address": "Address", "no_address": "No address", "edit_customer": "Edit Customer", "edit_tag": "Edit Tag", "assign_customer_tags": "Assign customer tags", "select_customer_tags": "Select customer tags", "overdue_ticket": "Overdue Ticket", "no_tickets_found": "No tickets found.", "total_policies": "Total Policies", "waiting_period": "Waiting Period", "nearly_expired": "Nearly Expired", "expired": "Expired", "policy_details": "Policy Details", "product_name": "Product Name", "product_type": "Product Type", "issue_date": "Issue Date", "see_more": "See more", "see_less": "See less", "drop_file_here": "Drop file here", "type_a_message_placeholder": "Type a message", "ai_response": "AI Response", "connected": "Connected", "disconnected": "Disconnected", "transfer_ticket_ownership": "Transfer Ticket Ownership", "change_status": "Change Status", "information": "Information", "summary": "Summary", "ai_guidance": "AI Guidance", "customer_details_description": "Access key information about the customer related to the current ticket.", "customer_notes": "Customer Notes", "customer_notes_description": "See the notes written about this customer.", "ticket_details": "Ticket Details", "ticket_details_description": "View and manage all relevant information associated with individual support tickets.", "previous_staffs": "Previous Staffs", "previous_staffs_description": "See the history of past staff members who have handled this ticket.", "customer_number": "Customer Number", "line_name": "Line Name", "not_specified": "Not specified", "optional": "Optional", "csat_settings": "Customer Satisfaction Survey Settings", "csat_description": "Upload and customize the image used for customer satisfaction surveys.", "upload_csat_image": "Upload CSAT Image", "save_csat_image": "Save CSAT Image", "all_conversations": "All Conversations", "select_conversation": "Select a conversation", "choose_platform_identity": "Choose a platform identity from the list to view messages", "search_platform": "Search", "select_conversation_view_details": "Select a conversation to view customer details", "chat_center": "Chat Center", "chat_center_all_messages": "All Messages", "chat_center_unread_messages": "Unread Messages", "chat_center_filter_daterange": "Date Range", "chat_center_filter_daterange_today": "Today", "chat_center_filter_daterange_week": "This Week", "chat_center_filter_daterange_1month": "This Month", "chat_center_filter_daterange_3months": "Last 3 Months", "chat_center_filter_daterange_6months": "Last 6 Months", "chat_center_filter_status": "Status", "chat_center_filter_status_all": "All Status", "chat_center_filter_tag": "Tag", "chat_center_filter_tag_all": "All Tags", "chat_center_filter_channel": "Channel", "chat_center_filter_channel_all": "All Channels", "chat_center_filter_button": "Filter", "chat_center_clear_button": "Clear", "timeline": "Timeline", "ai_assistant": "AI Assistant", "ask_ai": "Ask AI", "personalized_chatbot": "Personalized Chatbot", "restart_conversation": "Restart Conversation", "template_response": "Template Response", "new_template": "New Template", "quick_search": "Quick Search", "smart_reply": "Smart Reply", "search_type": "Search Type", "content_type": "Content Type", "quick_action": "Quick Actions", "copy": "Copy", "password_validation_msg_1": "Password must contain:", "password_validation_msg_2": "• At least 8 characters", "password_validation_msg_3": "• At least one lowercase and one uppercase letter", "password_validation_msg_4": "• At least one number", "password_validation_msg_5": "• At least one special character", "password_validation_msg_do_not_match": "Passwords do not match", "signup_form_required_badge": "Required", "signup_form_required_description": "All fields in this section are required", "signup_form_optional_badge": "Optional", "signup_form_optional_description": "Contact information for better communication", "signup_form_tab_personal_info": "Personal Info", "signup_form_tab_contact_details": "Contact Details", "signup_form_personal_phone": "Personal Phone", "signup_form_personal_email": "Personal Email", "signup_form_work_phone": "Work Phone", "signup_form_work_email": "Work Email", "signup_form_tab_preference": "Preferences", "signup_form_preferred_language": "Interface Language", "signup_form_preferred_language_placeholder": "Select Language", "signup_form_tab_emergency_contact": "Emergency Contact", "signup_form_emergency_name": "Emergency Contact Name", "signup_form_emergency_phone": "Emergency Contact Phone", "signup_form_emergency_email": "Emergency Contact Email", "signup_error_duplicated_username": "A user with that username already exists.", "signup_error_duplicated_email": "This email is already registered with another user.", "signup_error_invalid_email": "Please enter a valid email address.", "signup_success_toast": "New account created successfully.", "user_edit_menu": "Edit", "user_edit_user": "Edit User", "user_assign_tag": "Assign <PERSON>s", "user_assign_partner": "Assign Partners", "user_assign_role": "Assign Roles", "user_assign_department": "Assign Departments", "user_assign_line_account": "Assign L<PERSON><PERSON> Account", "user_deactivate_user": "Deactivate User", "user_deactivate_instructions_p1": "To confirm, please type", "user_deactivate_instructions_p2": "in the box below.", "user_deactivate_placeholder": "Enter username to confirm", "user_deactivate_validation_error": "Username does not match. Please type the exact username to confirm deactivation.", "user_deactivate_button": "Deactivate", "user_deactivate_success": "User deactivated successfully", "user_reactivate_user": "Reactivate User", "user_reactivate_instructions_p1": "To confirm, please type", "user_reactivate_instructions_p2": "in the box below.", "user_reactivate_placeholder": "Enter username to confirm", "user_reactivate_validation_error": "Username does not match. Please type the exact username to confirm reactivation.", "user_reactivate_button": "Reactivate", "user_reactivate_success": "User reactivated successfully", "loading_notes": "Loading notes...", "staff_history": "Staff history", "customer_tags": "Customer Tags", "full_name": "Full Name", "customer_type": "Customer Type", "country": "Country", "company": "Company", "contact_channel": "Contact Channel", "not_provided": "Not provided", "no_notes_available": "No notes available", "new_note": "Enter your note here...", "add_note": "Add Note", "edit": "Edit", "sort": "Sort", "restore_to_default": "Restore to Default", "date_uploaded": "Date Uploaded", "all_filters": "All Filters", "filter": "Filter", "add_filter": "Add Filter", "after_start_date_upload": "After Start Date Upload", "before_end_date_upload": "Before End Date Upload", "after_promo_start_date": "After Promo Start Date", "before_promo_end_date": "Before Promo End Date", "selected": "selected", "filename": "Filename", "image_name": "Image Name", "no_documents": "There are no documents available for display at this time.", "current": "Current", "assigned_on": "Assigned On", "employee": "Agents", "loading_customer_summary": "Loading customer summary...", "error_loading_summary": "Error Loading Summary", "no_analysis_available": "No Analysis Available", "ticket_analysis_placeholder": "Ticket analysis summaries will appear here", "ticket_summary": "Ticket Summary", "all_events": "All Events", "transfers": "Transfers", "closures": "Closures", "open": "Open", "try_again": "Try Again", "address_line1": "Address Line 1", "address_line2": "Address Line 2", "city": "City", "state_province_region": "State / Province / Region", "zip_code": "ZIP / Postal Code", "date_of_birth": "Date of Birth", "personal_information": "Personal Information", "address_information": "Address Information", "chatbot_workflow": "Chatbot Workflow", "chatbot_workflow_description": "This section will guide you through how the chatbot processes and handles customer interactions.", "underprogress": "This feature is under development.", "close-ticket": "Close Ticket", "transfer-ticket": "Transfer Ticket", "translation": "Translation", "language_name_en": "English", "language_name_th": "Thai", "chat_integrations": "Chat Integrations", "chat_integrations_description": "Number of connected accounts included in your plan:", "discover": "Discover", "line_official_account": "Line Official Account", "connect_line_description": "Connect at least 1 account for each", "facebook_instagram": "Facebook / Instagram", "whatsapp_business": "WhatsApp Business", "shopee": "<PERSON>ee", "lazada": "<PERSON><PERSON><PERSON>", "tiktok_shop": "TikTok Shop", "connect_multiple_description": "Connect multiple accounts", "connect": "Connect", "not_available_yet": "Not available yet", "no_owner_history_available": "No owner history available.", "search_filename": "Search Filename", "pending_to_close": "Pending to Close", "closed": "Closed", "connect_line_business": "Connect LINE Business", "line_business_configuration_description": "Enter your LINE Business configuration details to establish the connection.", "connection_name": "Connection Name", "connection_name_placeholder": "My LINE Business Account", "connection_name_description": "A friendly name for this LINE connection", "channel_id": "Channel ID", "channel_id_placeholder": "**********", "channel_id_description": "Your LINE Channel ID from LINE Developers Console", "channel_secret": "Channel Secret", "channel_secret_placeholder": "••••••••••••••••", "channel_secret_description": "Your LINE Channel Secret", "channel_access_token": "Channel Access Token", "channel_access_token_placeholder": "••••••••••••••••", "channel_access_token_description": "Your LINE Channel Access Token", "line_provider_id": "LINE Provider ID", "line_provider_id_placeholder": "provider-123", "line_provider_id_description": "Your LINE Provider ID", "line_provider_name": "LINE Provider Name", "line_provider_name_placeholder": "My Business Provider", "line_provider_name_description": "Your LINE Provider Name", "verification_status": "Verification Status", "not_verified": "Not Verified", "verified": "Verified", "verification_status_description": "Select whether this connection has been verified", "webhook_settings_description": "*Click copy to use this link in Webhook settings (URL)", "connection_saved_successfully": "Connection saved successfully!", "webhook_url_copied": "Webhook URL copied to clipboard!", "verify": "Verify", "manage_line_connection": "Manage Line Connection", "connection_status": "Connectorion Status", "connection_active": "Connection Active", "connection_disabled": "Connection Disabled", "test_connection": "Test Connection", "manage": "Manage", "webhook_url": "Webhook URL", "there_are": "There are", "accounts_has_been_connected": "have been connected", "enable_auto_notification": "Enable Auto-Notification", "response_time_limit": "Response Time Limit", "response_time_limit_description": "If no admin responds within this time, the notification will be sent", "auto_notification_message": "Auto-Notification Message", "auto_notification_message_placeholder": "Enter the message to be sent when auto-notification is triggered...", "auto_notification_message_description": "This message will be sent to notify when no response is received within the time limit", "chatbot_behavior_outside_business_hours": "Chatbot Behavior Outside Business Hours", "configure_automatic_message_outside_hours": "Configure the automatic message sent to customers when they contact outside business hours.", "automatic_message_outside_hours_description": "This message will be automatically sent to customers who contact you outside your business hours.", "visit_link": "Visit Link", "click_to_visit_link": "Click to visit link", "qr_codes_links_description": "Upload QR codes and add links for LINE Official Account and LINE Group to complete the connection setup.", "qr_code": "QR Code", "line_oa_link": "LINE OA Link", "line_group_link": "LINE Group Link", "qr_codes_and_links": "QR Codes & Links", "connection_details": "Connection Details", "line_oa_link_placeholder": "Enter LINE Official Account link", "line_group_link_placeholder": "Enter LINE Group link", "type_message": "Type a message...", "csat_rating_system": "Customer Satisfaction Rating System", "csat_rating_description": "Customers can choose from 5 satisfaction levels as follows:", "excellent": "Excellent", "good": "Good", "ok": "OK", "poor": "Poor", "need_improvement": "Need Improvement", "five_points": "5 Points", "four_points": "4 Points", "three_points": "3 Points", "two_points": "2 Points", "one_point": "1 Point", "csat_calculation_note": "The system will calculate the average satisfaction score from all customer ratings", "upload_logo_note": "The system will display the logo on the sidebar.", "line_group_connection_description": "Save your QR code and link for LINE Group connection.", "setup_instructions": "Setup Instructions", "create_line_group": "Create LINE Group", "invite_chatbot": "Invite chatbot to LINE Group", "invite_employees": "Invite employees to LINE Group", "employees_connect_account": "Employees connect their LINE account in settings", "important_note": "Important", "unconnected_warning": "If employees haven't connected their LINE accounts, the chatbot will not be able to notify them in the LINE group", "saving_settings": "Saving settings...", "settings_saved_successfully": "Setting<PERSON> saved successfully!", "error": "Error", "save_failed": "Unable to save", "download": "Download", "message_template_empty_or_invalid": "Message template is empty or invalid.", "no_messages_yet": "No messages yet. Start a conversation!", "no_tags_available": "No tags available. Please create tags in settings first.", "partner_name_placeholder": "Enter Partner Name...", "partner_abbreviation_placeholder": "Enter Partner Abbreviation..."}